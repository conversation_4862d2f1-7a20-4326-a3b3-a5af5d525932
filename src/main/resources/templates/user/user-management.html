<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">

<head>
    <title>User Management</title>
    <meta name="description" content="Manage system users"/>
</head>

<body>

<th:block layout:fragment="optionalVendorCSS">
    <link rel="stylesheet" th:href="@{/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css}"/>
    <link rel="stylesheet" th:href="@{/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css}"/>
    <link rel="stylesheet" th:href="@{/assets/vendor/libs/sweetalert2/sweetalert2.css}"/>
    <link rel="stylesheet" th:href="@{/css/toast-styles.css}"/>
    <style>
        /* Custom styling for user form offcanvas */
        #userFormOffcanvas {
            width: 500px !important;
            max-width: 500px !important;
        }

        #userFormOffcanvas.offcanvas-end {
            right: 0;
            transform: translateX(100%);
        }

        #userFormOffcanvas.offcanvas-end.show {
            transform: translateX(0);
        }

        .offcanvas-body div {
            padding: 0;
        }

        .offcanvas-footer {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        /* Ensure proper offcanvas behavior */
        .offcanvas {
            position: fixed;
            top: 0;
            z-index: 1045;
            display: flex;
            flex-direction: column;
            max-width: 100%;
            height: 100vh;
            visibility: hidden;
            background-color: var(--bs-offcanvas-bg);
            background-clip: padding-box;
            outline: 0;
            transition: transform 0.3s ease-in-out;
        }

        .offcanvas.show {
            visibility: visible;
        }

        @media (max-width: 768px) {
            #userFormOffcanvas {
                width: 100% !important;
                max-width: 100% !important;
            }
        }

        /* HTMX Loading Indicator */
        .htmx-indicator {
            display: none;
        }

        .htmx-request .htmx-indicator {
            display: inline-block;
        }

        .htmx-request.htmx-indicator {
            display: inline-block;
        }

        /* Modal and Offcanvas compatibility */
        .swal2-container {
            z-index: 2000 !important; /* Higher than offcanvas z-index */
        }

        .swal2-backdrop-show {
            z-index: 1999 !important;
        }

        /* Ensure offcanvas doesn't interfere with modals */
        .offcanvas {
            z-index: 1045; /* Lower than SweetAlert modals */
        }

        /* Body scroll lock styles */
        body.modal-open {
            overflow: hidden !important;
            position: fixed !important;
            width: 100% !important;
        }

        /* Prevent layout shift when body scroll is locked */
        .scroll-locked {
            padding-right: 0 !important;
        }

        /* Accessibility improvements */
        .swal2-container {
            outline: none; /* Remove default outline as SweetAlert handles focus internally */
        }

        /* Ensure focus is visible on interactive elements */
        .btn:focus,
        .form-control:focus,
        .form-select:focus {
            outline: 2px solid #0d6efd;
            outline-offset: 2px;
        }

        /* Hide elements from screen readers when modal is open */
        body.swal2-shown [aria-hidden="true"] {
            visibility: hidden;
        }

        /* Ensure offcanvas content is properly focusable */
        .offcanvas-body input:first-of-type,
        .offcanvas-body select:first-of-type,
        .offcanvas-body textarea:first-of-type {
            outline: none; /* Will be focused programmatically */
        }

        /* Modal over offcanvas styling */
        .swal2-container-over-offcanvas {
            z-index: 2050 !important; /* Higher than offcanvas z-index (1045) */
        }

        .swal2-container-over-offcanvas .swal2-backdrop-show {
            z-index: 2049 !important;
        }

        /* Form validation styling */
        .invalid-feedback {
            display: block !important;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #ea5455 !important; /* Red color for validation messages */
            font-weight: 500;
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: #ea5455 !important;
            box-shadow: 0 0 0 0.2rem rgba(234, 84, 85, 0.25) !important;
        }

        .form-check-input.is-invalid {
            border-color: #ea5455 !important;
        }

        .form-check-input.is-invalid:checked {
            background-color: #ea5455 !important;
            border-color: #ea5455 !important;
        }

        /* Password toggle button styling */
        .input-group .btn-outline-secondary {
            border-color: #d0d7de;
            color: #656d76;
            background-color: transparent;
        }

        .input-group .btn-outline-secondary:hover {
            background-color: #f6f8fa;
            border-color: #d0d7de;
            color: #24292f;
        }

        .input-group .btn-outline-secondary:focus {
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
            border-color: #86b7fe;
        }

        .input-group .btn-outline-secondary i {
            font-size: 1rem;
            line-height: 1;
        }

        /* Ensure password field with toggle button has proper validation styling */
        .input-group .form-control.is-invalid {
            border-right: 1px solid #ea5455;
        }

        .input-group .form-control.is-invalid + .btn {
            border-color: #ea5455;
        }

        /* Placeholder rows styling to maintain consistent table height */
        .placeholder-row {
            height: 73px; /* Same height as regular user rows */
            border-bottom: 1px solid #e7eef7;
        }

        .placeholder-row td {
            padding: 0.75rem 1.25rem;
            vertical-align: middle;
            border-top: none;
            border-bottom: 1px solid #e7eef7;
        }

        .placeholder-row:hover {
            background-color: transparent !important;
        }
    </style>
</th:block>

<div layout:fragment="content" class="container-xxl flex-grow-1 container-p-y">

    <!-- Page Header -->
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-6">
                <div>
                    <h4 class="mb-1" th:text="#{user.management.title}">User Management</h4>
                    <p class="mb-0" th:text="#{user.management.subtitle}">Manage system users, roles, and permissions</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button"
                            id="refreshButton"
                            class="btn btn-outline-secondary"
                            hx-get="/admin/users/refresh"
                            hx-target="#userContent"
                            hx-swap="outerHTML"
                            hx-indicator="#refreshSpinner"
                            hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter, #currentPageInput"
                            th:title="#{user.button.refresh}">
                        <i class="ti ti-refresh me-1"></i><span th:text="#{user.button.refresh}">Refresh</span>
                    </button>
                    <button type="button"
                            class="btn btn-primary"
                            hx-get="/admin/users/form"
                            hx-target="#userFormOffcanvas .offcanvas-body"
                            hx-swap="innerHTML"
                            hx-trigger="click"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#userFormOffcanvas"
                            aria-controls="userFormOffcanvas"
                            th:attr="data-title=#{user.button.add}">
                        <i class="ti ti-plus me-1"></i><span th:text="#{user.button.add}">Add User</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Content (Statistics + List) -->
    <div id="userContent" th:replace="~{user/fragments/user-content :: userContent}"></div>

    <!-- User Form Offcanvas -->
    <div class="offcanvas offcanvas-end"
         id="userFormOffcanvas"
         tabindex="-1"
         aria-labelledby="userFormOffcanvasLabel"
         aria-describedby="userFormOffcanvasDescription"
         data-bs-backdrop="true"
         data-bs-scroll="false"
         role="dialog"
         aria-modal="true">
        <div class="offcanvas-header">
            <div class="d-flex flex-column">
                <h5 class="offcanvas-title" id="userFormOffcanvasLabel">User Form</h5>
                <p class="visually-hidden" id="userFormOffcanvasDescription">Form for adding or editing user information</p>
            </div>
            <button type="button"
                    class="btn-close"
                    data-bs-dismiss="offcanvas"
                    th:aria-label="#{modal.close}"></button>
        </div>
        <div class="offcanvas-body">
            <!-- Content will be loaded via HTMX -->
        </div>
    </div>

    <!-- Response container for modal messages -->
    <div id="response"></div>


</div>

<th:block layout:fragment="optionalVendorJS">
    <script th:src="@{/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js}"></script>
    <script th:src="@{/assets/vendor/libs/sweetalert2/sweetalert2.js}"></script>
</th:block>

<th:block layout:fragment="optionalPageJS">
    <script th:src="@{/js/htmx-extensions/bsSend.js}"></script>
    <script th:src="@{/js/modalResponse.js}"></script>
    <script th:src="@{/js/toast-config.js}"></script>

    <script th:inline="javascript">

        // Function to refresh user list while preserving current filters and pagination
        function refreshUserListWithCurrentState() {
            // Get current filter values
            const fullNameFilter = document.getElementById('fullNameFilter');
            const emailFilter = document.getElementById('emailFilter');
            const companyFilter = document.getElementById('companyFilter');
            const roleFilter = document.getElementById('roleFilter');
            const enabledFilter = document.getElementById('enabledFilter');
            const accountLockedFilter = document.getElementById('accountLockedFilter');
            const currentPageInput = document.getElementById('currentPageInput');

            // Build query parameters
            const params = new URLSearchParams();

            // Preserve current page
            const currentPage = currentPageInput ? currentPageInput.value : '1';
            params.append('page', currentPage);

            // Preserve all filter values
            if (fullNameFilter && fullNameFilter.value) params.append('fullName', fullNameFilter.value);
            if (emailFilter && emailFilter.value) params.append('email', emailFilter.value);
            if (companyFilter && companyFilter.value) params.append('company', companyFilter.value);
            if (roleFilter && roleFilter.value) params.append('role', roleFilter.value);
            if (enabledFilter && enabledFilter.value) params.append('enabled', enabledFilter.value);
            if (accountLockedFilter && accountLockedFilter.value) params.append('accountLocked', accountLockedFilter.value);

            // Show loading indicator
            const refreshSpinner = document.getElementById('refreshSpinner');
            if (refreshSpinner) {
                refreshSpinner.style.display = 'inline-block';
            }

            // Trigger refresh of the entire user content (statistics + list) with current state preserved
            htmx.ajax('GET', '/admin/users/refresh?' + params.toString(), {
                target: '#userContent',
                swap: 'outerHTML'
            }).then(function() {
                // Hide loading indicator after refresh completes
                const refreshSpinner = document.getElementById('refreshSpinner');
                if (refreshSpinner) {
                    refreshSpinner.style.display = 'none';
                }
            }).catch(function(error) {
                console.error('Error refreshing user list:', error);
                // Hide loading indicator on error
                const refreshSpinner = document.getElementById('refreshSpinner');
                if (refreshSpinner) {
                    refreshSpinner.style.display = 'none';
                }
            });
        }

        // Make the function globally available for HTMX inline handlers
        window.refreshUserListWithCurrentState = refreshUserListWithCurrentState;

        // Password visibility toggle function
        function togglePasswordVisibility(passwordFieldId, toggleButtonId) {
            const passwordField = document.getElementById(passwordFieldId);
            const toggleButton = document.getElementById(toggleButtonId);
            const toggleIcon = toggleButton.querySelector('i');

            if (passwordField && toggleIcon) {
                if (passwordField.type === 'password') {
                    // Show password
                    passwordField.type = 'text';
                    toggleIcon.className = 'ti ti-eye-off';
                    toggleButton.title = 'Hide Password';
                } else {
                    // Hide password
                    passwordField.type = 'password';
                    toggleIcon.className = 'ti ti-eye';
                    toggleButton.title = 'Show Password';
                }
            }
        }

        // Make the function globally available for inline handlers
        window.togglePasswordVisibility = togglePasswordVisibility;

        // Global password validation function
        window.validatePasswordField = function(fieldId) {
            const field = document.getElementById(fieldId);
            const value = field.value;
            const feedbackElement = document.getElementById(fieldId + '-feedback');

            // Requirements elements
            const reqLength = document.getElementById('req-length');
            const reqLowercase = document.getElementById('req-lowercase');
            const reqUppercase = document.getElementById('req-uppercase');
            const reqNumber = document.getElementById('req-number');
            const reqSpecial = document.getElementById('req-special');

            if (!value) {
                // Reset all requirements to invalid state when field is empty
                resetRequirement(reqLength);
                resetRequirement(reqLowercase);
                resetRequirement(reqUppercase);
                resetRequirement(reqNumber);
                resetRequirement(reqSpecial);

                field.classList.remove('is-valid', 'is-invalid');
                if (feedbackElement) feedbackElement.innerHTML = '';
                return;
            }

            // Check each requirement
            const hasLength = value.length >= 8;
            const hasLowercase = /[a-z]/.test(value);
            const hasUppercase = /[A-Z]/.test(value);
            const hasNumber = /\d/.test(value);
            const hasSpecial = /[@#$!%*?&.;:<>]/.test(value);

            // Update requirement indicators
            updateRequirement(reqLength, hasLength);
            updateRequirement(reqLowercase, hasLowercase);
            updateRequirement(reqUppercase, hasUppercase);
            updateRequirement(reqNumber, hasNumber);
            updateRequirement(reqSpecial, hasSpecial);

            // Overall validation
            const isValid = hasLength && hasLowercase && hasUppercase && hasNumber && hasSpecial;

            if (isValid) {
                field.classList.add('is-valid');
                field.classList.remove('is-invalid');
                field.setCustomValidity('');
                if (feedbackElement) feedbackElement.innerHTML = '';
            } else {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                field.setCustomValidity('Password does not meet requirements');

                // Show specific error message
                const errors = [];
                if (!hasLength) errors.push('At least 8 characters');
                if (!hasLowercase) errors.push('One lowercase letter');
                if (!hasUppercase) errors.push('One uppercase letter');
                if (!hasNumber) errors.push('One number');
                if (!hasSpecial) errors.push('One special character');

                if (feedbackElement) {
                    feedbackElement.innerHTML = 'Password must contain: ' + errors.join(', ');
                }
            }
        };

        function updateRequirement(element, isValid) {
            if (!element) return;

            const icon = element.querySelector('i');
            if (isValid) {
                element.classList.add('valid');
                element.classList.remove('invalid');
                if (icon) {
                    icon.className = 'ti ti-check text-success';
                }
            } else {
                element.classList.add('invalid');
                element.classList.remove('valid');
                if (icon) {
                    icon.className = 'ti ti-x text-danger';
                }
            }
        }

        function resetRequirement(element) {
            if (!element) return;

            element.classList.remove('valid', 'invalid');
            const icon = element.querySelector('i');
            if (icon) {
                icon.className = 'ti ti-x text-danger';
            }
        }

        // Global role validation function
        window.validateRoleSelection = function(formType) {
            const isCurrentUserAdmin = /*[[${isCurrentUserAdmin}]]*/ false;

            // Skip validation if current user is admin
            if (isCurrentUserAdmin) {
                return true;
            }

            const prefix = formType === 'edit' ? 'editRole_' : 'role_';
            const feedbackId = formType === 'edit' ? 'editRolesInvalidFeedback' : 'rolesInvalidFeedback';
            const containerId = formType === 'edit' ? 'edit-roles-container' : 'roles-container';

            // Get all role radio buttons for this form
            const roleRadios = document.querySelectorAll(`input[name="roles"][id^="${prefix}"]`);
            const feedbackElement = document.getElementById(feedbackId);
            const container = document.getElementById(containerId);

            // Check if any role is selected
            const isAnyRoleSelected = Array.from(roleRadios).some(radio => radio.checked);

            if (!isAnyRoleSelected) {
                // No role selected - show error
                roleRadios.forEach(radio => {
                    radio.classList.add('is-invalid');
                    // Set custom validity to integrate with HTML5 validation
                    radio.setCustomValidity('Please select a role');
                });
                if (container) {
                    container.classList.add('is-invalid');
                }
                if (feedbackElement) {
                    feedbackElement.style.display = 'block';
                    feedbackElement.classList.add('d-block');
                }
                return false;
            } else {
                // Role selected - clear error
                roleRadios.forEach(radio => {
                    radio.classList.remove('is-invalid');
                    // Clear custom validity
                    radio.setCustomValidity('');
                });
                if (container) {
                    container.classList.remove('is-invalid');
                }
                if (feedbackElement) {
                    feedbackElement.style.display = 'none';
                    feedbackElement.classList.remove('d-block');
                }
                return true;
            }
        };

        // Global alert functions
        window.showUserFormAlert = function(type, title, message) {
            const alertBox = document.getElementById('userFormAlert');
            const alertIcon = document.getElementById('userFormAlertIcon');
            const alertTitle = document.getElementById('userFormAlertTitle');
            const alertMessage = document.getElementById('userFormAlertMessage');

            if (!alertBox || !alertIcon || !alertTitle || !alertMessage) {
                console.error('Alert box elements not found');
                return;
            }

            // Clear existing classes
            alertBox.className = 'alert alert-dismissible fade show';

            // Set type-specific styling and icon
            switch (type) {
                case 'error':
                case 'danger':
                    alertBox.classList.add('alert-danger');
                    alertIcon.className = 'ti ti-alert-circle me-2';
                    break;
                case 'warning':
                    alertBox.classList.add('alert-warning');
                    alertIcon.className = 'ti ti-alert-triangle me-2';
                    break;
                case 'info':
                    alertBox.classList.add('alert-info');
                    alertIcon.className = 'ti ti-info-circle me-2';
                    break;
                case 'success':
                    alertBox.classList.add('alert-success');
                    alertIcon.className = 'ti ti-check-circle me-2';
                    break;
                default:
                    alertBox.classList.add('alert-danger');
                    alertIcon.className = 'ti ti-alert-circle me-2';
            }

            // Set content
            alertTitle.textContent = title || 'Error';
            alertMessage.textContent = message || 'An error occurred';

            // Show the alert
            alertBox.style.display = 'block';

            // Scroll to top of form to ensure alert is visible
            const formContainer = alertBox.closest('.offcanvas-body');
            if (formContainer) {
                formContainer.scrollTop = 0;
            }

        };

        window.hideUserFormAlert = function() {
            const alertBox = document.getElementById('userFormAlert');
            if (alertBox) {
                alertBox.style.display = 'none';
                alertBox.className = 'alert alert-dismissible fade';
            }
        };

        // Initialize form validation when forms are loaded via HTMX
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.classList.contains('offcanvas-body')) {
                initializeUserFormValidation();
                // Hide any existing alert when form is loaded
                hideUserFormAlert();
            }
        });

        // HTMX Error Handling for User Forms (only for 500+ errors, 400 errors are handled by handleUserFormResponse)
        document.body.addEventListener('htmx:responseError', function(event) {
            const form = event.target;
            if (form && (form.id === 'createUserForm' || form.id === 'editUserForm')) {

                // Only handle 500+ errors here, 400 errors are handled by handleUserFormResponse
                if (event.detail.xhr.status >= 500) {
                    let errorTitle = /*[[#{alert.error.title}]]*/ 'Error';
                    let errorMessage = /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request';

                // Try to parse error response
                try {
                    if (event.detail.xhr && event.detail.xhr.responseText) {
                        const response = JSON.parse(event.detail.xhr.responseText);

                        // Use the server-provided title and message directly
                        if (response.title) errorTitle = response.title;
                        if (response.text) errorMessage = response.text;

                        // The server should already provide localized messages, so we don't need to override them
                        // unless they're not provided
                        if (!response.title) {
                            errorTitle = /*[[#{alert.error.title}]]*/ 'Error';
                        }
                        if (!response.text) {
                            errorMessage = /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request';
                        }
                    }
                } catch (e) {
                    // If we can't parse JSON, use fallback messages
                    if (event.detail.xhr && event.detail.xhr.responseText) {
                        const responseText = event.detail.xhr.responseText;
                        errorMessage = responseText.substring(0, 200) + (responseText.length > 200 ? '...' : '');
                    }
                }
                    showUserFormAlert('error', errorTitle, errorMessage);
                } else {
                    // 400-level error, will be handled by handleUserFormResponse
                }
            }
        });

        // HTMX Network Error Handling
        document.body.addEventListener('htmx:sendError', function(event) {
            const form = event.target;
            if (form && (form.id === 'createUserForm' || form.id === 'editUserForm')) {
                console.log('HTMX send error for user form:', event.detail);
                showUserFormAlert('error', /*[[#{alert.error.title}]]*/ 'Error', /*[[#{alert.error.network}]]*/ 'Unable to connect to the server. Please check your internet connection');
            }
        });

        // HTMX Timeout Handling
        document.body.addEventListener('htmx:timeout', function(event) {
            const form = event.target;
            if (form && (form.id === 'createUserForm' || form.id === 'editUserForm')) {
                console.log('HTMX timeout for user form:', event.detail);
                showUserFormAlert('warning', /*[[#{alert.warning.title}]]*/ 'Warning', /*[[#{alert.error.timeout}]]*/ 'The request is taking longer than expected. Please try again');
            }
        });

        // Clear alert when form is successfully submitted
        document.body.addEventListener('htmx:afterRequest', function(event) {
            const form = event.target;
            if (form && (form.id === 'createUserForm' || form.id === 'editUserForm')) {
                // Only hide alert if request was successful
                if (event.detail.successful) {
                    hideUserFormAlert();
                }
            }
        });

        // Handle HTMX responses for user forms
        document.body.addEventListener('htmx:afterRequest', function(event) {
            const requestPath = event.detail.pathInfo.requestPath;

            // Handle user form submissions (create/update)
            if (requestPath.includes('/admin/users/create') || requestPath.includes('/admin/users/update')) {
                // Clear form validation state
                const form = event.detail.elt;
                if (form && form.classList.contains('user-form')) {
                    form.classList.remove('was-validated');
                }

                // Handle the response
                handleUserFormResponse(event);
            }
        });

        // Clear offcanvas content when it's hidden
        document.getElementById('userFormOffcanvas').addEventListener('hidden.bs.offcanvas', function() {
            // Clear the offcanvas body content
            const offcanvasBody = this.querySelector('.offcanvas-body');
            if (offcanvasBody) {
                offcanvasBody.innerHTML = '<!-- Content will be loaded via HTMX -->';
            }

            // Reset the title
            const titleElement = document.getElementById('userFormOffcanvasLabel');
            if (titleElement) {
                titleElement.textContent = 'User Form';
            }

            // Clear any response messages
            const responseContainer = document.getElementById('response');
            if (responseContainer) {
                responseContainer.innerHTML = '';
            }
        });

        // Handle form response and close offcanvas on success
        function handleUserFormResponse(event) {
            const xhr = event.detail.xhr;

            if (xhr.status === 200) {
                // Parse the response to check if it's a success message
                try {
                    const responseText = xhr.responseText;
                    const responseObj = JSON.parse(responseText);

                    // Check if it's a success response (icon: "success")
                    if (responseObj.icon === "success") {
                        // Show success toast
                        showToastFromResponse(responseObj);

                        // Close the offcanvas
                        const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('userFormOffcanvas'));
                        if (offcanvas) {
                            offcanvas.hide();
                        }

                        // Show loading indicator if available
                        const refreshSpinner = document.getElementById('refreshSpinner');
                        if (refreshSpinner) {
                            refreshSpinner.style.display = 'inline-block';
                        }

                        // Refresh the user list after a short delay to allow modal to show
                        setTimeout(function() {
                            // Get current filter values
                            const fullNameFilter = document.getElementById('fullNameFilter');
                            const emailFilter = document.getElementById('emailFilter');
                            const companyFilter = document.getElementById('companyFilter');
                            const roleFilter = document.getElementById('roleFilter');
                            const enabledFilter = document.getElementById('enabledFilter');
                            const accountLockedFilter = document.getElementById('accountLockedFilter');
                            const currentPageInput = document.getElementById('currentPageInput');

                            // Build query parameters
                            const params = new URLSearchParams();

                            // Preserve current page instead of resetting to 1
                            const currentPage = currentPageInput ? currentPageInput.value : '1';
                            params.append('page', currentPage);

                            if (fullNameFilter && fullNameFilter.value) params.append('fullName', fullNameFilter.value);
                            if (emailFilter && emailFilter.value) params.append('email', emailFilter.value);
                            if (companyFilter && companyFilter.value) params.append('company', companyFilter.value);
                            if (roleFilter && roleFilter.value) params.append('role', roleFilter.value);
                            if (enabledFilter && enabledFilter.value) params.append('enabled', enabledFilter.value);
                            if (accountLockedFilter && accountLockedFilter.value) params.append('accountLocked', accountLockedFilter.value);

                            // Trigger refresh of the entire user content (statistics + list) with current filters and pagination
                            htmx.ajax('GET', '/admin/users/refresh?' + params.toString(), {
                                target: '#userContent',
                                swap: 'outerHTML'
                            }).then(function() {
                                // Hide loading indicator after refresh completes
                                const refreshSpinner = document.getElementById('refreshSpinner');
                                if (refreshSpinner) {
                                    refreshSpinner.style.display = 'none';
                                }
                            });
                        }, 500);
                    } else {
                        // Error response - show alert box
                        showUserFormAlert('error', responseObj.title || /*[[#{alert.error.title}]]*/ 'Error', responseObj.text || /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request');
                    }
                } catch (e) {
                    // If response is not JSON, show generic error alert box
                    showUserFormAlert('error', /*[[#{alert.error.title}]]*/ 'Error', /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request');
                }
            } else if (xhr.status === 400) {
                // Handle validation errors - try to parse and show field-specific errors
                try {
                    const responseText = xhr.responseText;
                    const responseObj = JSON.parse(responseText);

                    // Check if it contains field validation errors
                    if (responseObj.fieldErrors) {
                        handleFieldValidationErrors(responseObj.fieldErrors);
                    } else {
                        // Determine alert type based on the error message
                        let alertType = 'warning';
                        if (responseObj.text && (responseObj.text.includes('already exists') || responseObj.text.includes('já existe'))) {
                            alertType = 'error'; // User already exists should be an error, not warning
                        }
                        showUserFormAlert(alertType, responseObj.title || /*[[#{alert.error.title}]]*/ 'Error', responseObj.text || /*[[#{alert.error.validation}]]*/ 'Please correct the validation errors and try again');
                    }
                } catch (e) {
                    // Show generic error alert box
                    showUserFormAlert('warning', /*[[#{alert.warning.title}]]*/ 'Warning', /*[[#{alert.error.validation}]]*/ 'Please correct the validation errors and try again');
                }
            } else {
                // Show error alert box for other non-200 responses
                showUserFormAlert('error', /*[[#{alert.error.title}]]*/ 'Error', /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request');
            }
        }



        // Handle field-specific validation errors
        function handleFieldValidationErrors(fieldErrors) {
            // Clear previous errors
            document.querySelectorAll('.invalid-feedback').forEach(el => {
                el.textContent = '';
                el.style.display = 'none';
            });
            document.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });

            // Show field-specific errors
            Object.keys(fieldErrors).forEach(fieldName => {
                const field = document.querySelector(`[name="${fieldName}"]`);
                const feedback = field ? field.parentElement.querySelector('.invalid-feedback') : null;

                if (field && feedback) {
                    field.classList.add('is-invalid');
                    feedback.textContent = fieldErrors[fieldName];
                    feedback.style.display = 'block';
                }
            });

            // If no field-specific errors were shown, show generic modal
            const hasFieldErrors = Object.keys(fieldErrors).some(fieldName => {
                return document.querySelector(`[name="${fieldName}"]`);
            });

            if (!hasFieldErrors) {
                const errorMessage = Object.values(fieldErrors).join(', ');
                showUserFormAlert('warning', /*[[#{alert.warning.title}]]*/ 'Warning', errorMessage);
            }
        }

        function initializeUserFormValidation() {
            const createForm = document.getElementById('createUserForm');
            const editForm = document.getElementById('editUserForm');

            // Custom validation function for role radio buttons
            function validateRoles(form) {
                const isCurrentUserAdmin = /*[[${session.currentUser != null && session.currentUser.roles.?[name == T(ag.fuel.jobify.auth.entity.ERole).ROLE_ADMIN].size() > 0}]]*/ false;
                const isEditForm = form.id === 'editUserForm';
                const roleRadios = form.querySelectorAll('.role-radio');
                const isAnyRoleSelected = Array.from(roleRadios).some(radio => radio.checked);
                const feedbackElement = form.querySelector('#rolesInvalidFeedback, #editRolesInvalidFeedback');

                if (isEditForm) {
                    // Edit mode: New validation logic
                    if (!isAnyRoleSelected) {
                        // Zero roles selected - check if user being edited currently has ADMIN role
                        const isUserBeingEditedAdmin = form.querySelector('input[name="roles"][value="ROLE_ADMIN"]')?.checked || false;

                        if (isUserBeingEditedAdmin) {
                            // User being edited is ADMIN, allow saving as ADMIN (clear any errors)
                            roleRadios.forEach(radio => {
                                radio.classList.remove('is-invalid');
                            });
                            if (feedbackElement) {
                                feedbackElement.textContent = '';
                                feedbackElement.style.display = 'none';
                            }
                            return true;
                        } else {
                            // User being edited is not ADMIN, must select at least one role
                            roleRadios.forEach(radio => {
                                radio.classList.add('is-invalid');
                            });
                            if (feedbackElement) {
                                feedbackElement.textContent = /*[[#{user.validation.roles.required}]]*/ 'Role selection is required';
                                feedbackElement.style.display = 'block';
                            }
                            return false;
                        }
                    } else {
                        // At least one role is selected, proceed with validation (clear errors)
                        roleRadios.forEach(radio => {
                            radio.classList.remove('is-invalid');
                        });
                        if (feedbackElement) {
                            feedbackElement.textContent = '';
                            feedbackElement.style.display = 'none';
                        }
                        return true;
                    }
                } else {
                    // Create mode: Original logic
                    if (isCurrentUserAdmin) {
                        // Admin users don't need to select a role - clear any existing errors
                        roleRadios.forEach(radio => {
                            radio.classList.remove('is-invalid');
                        });
                        if (feedbackElement) {
                            feedbackElement.textContent = '';
                            feedbackElement.style.display = 'none';
                        }
                        return true;
                    }

                    if (!isAnyRoleSelected) {
                        // Show error
                        roleRadios.forEach(radio => {
                            radio.classList.add('is-invalid');
                        });
                        if (feedbackElement) {
                            feedbackElement.textContent = /*[[#{user.validation.roles.required}]]*/ 'Role selection is required';
                            feedbackElement.style.display = 'block';
                        }
                        return false;
                    } else {
                        // Clear error
                        roleRadios.forEach(radio => {
                            radio.classList.remove('is-invalid');
                        });
                        if (feedbackElement) {
                            feedbackElement.textContent = '';
                            feedbackElement.style.display = 'none';
                        }
                        return true;
                    }
                }
            }

            [createForm, editForm].forEach(function(form) {
                if (form) {
                    // Add role validation on radio button change
                    const roleRadios = form.querySelectorAll('.role-radio');
                    roleRadios.forEach(radio => {
                        radio.addEventListener('change', function() {
                            validateRoles(form);
                        });
                    });

                    // Add form validation before submit
                    form.addEventListener('submit', function(event) {
                        const isRolesValid = validateRoles(form);
                        if (!isRolesValid) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                    });

                    const cancelButton = form.querySelector('button[data-bs-dismiss="offcanvas"]');
                    if (cancelButton) {
                        cancelButton.addEventListener('click', function() {
                            // Clear form fields (only for create form)
                            if (form.id === 'createUserForm') {
                                form.reset();
                            }

                            // Clear invalid feedback elements
                            form.querySelectorAll('.invalid-feedback').forEach(function(element) {
                                element.innerHTML = '';
                                element.style.display = 'none';
                            });

                            // Remove validation classes
                            form.querySelectorAll('.is-invalid, .was-validated').forEach(function(element) {
                                element.classList.remove('is-invalid', 'was-validated');
                            });

                            // Remove was-validated from form itself
                            form.classList.remove('was-validated');
                        });
                    }
                }
            });

            // Re-initialize bsSend validation for dynamically loaded forms
            const forms = document.querySelectorAll('.needs-validation');
            forms.forEach(function (form) {
                // Remove existing event listeners to avoid duplicates
                const newForm = form.cloneNode(true);
                form.parentNode.replaceChild(newForm, form);

                // Add the validation event listener
                newForm.addEventListener('submit', function (event) {
                    if (newForm.checkValidity()) {
                        // trigger custom event hx-trigger="bs-send"
                        htmx.trigger(newForm, "bsSend");
                    }

                    event.preventDefault();
                    event.stopPropagation();

                    newForm.classList.add('was-validated');
                }, false);
            });
        }
    </script>
</th:block>


</body>
</html>
