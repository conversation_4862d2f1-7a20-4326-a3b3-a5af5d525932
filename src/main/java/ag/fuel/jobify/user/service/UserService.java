package ag.fuel.jobify.user.service;

import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.auth.entity.Role;
import ag.fuel.jobify.auth.repository.RoleRepository;
import ag.fuel.jobify.common.util.Constants;
import ag.fuel.jobify.communication.service.EmailService;
import ag.fuel.jobify.company.entity.Company;
import ag.fuel.jobify.company.service.CompanyService;
import ag.fuel.jobify.file.service.FilesStorageService;
import ag.fuel.jobify.security.entity.PasswordResetToken;
import ag.fuel.jobify.security.entity.UserActivationToken;
import ag.fuel.jobify.security.repository.PasswordResetTokenRepository;
import ag.fuel.jobify.security.repository.UserActivationTokenRepository;
import ag.fuel.jobify.user.dto.CreateUserDto;
import ag.fuel.jobify.user.dto.EditUserDto;
import ag.fuel.jobify.user.dto.UpdateUserDetailsDto;
import ag.fuel.jobify.user.dto.UserListDto;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UserService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Value("${application.base-url}")
    private String baseUrl;

    private final UserRepository userRepository;
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final UserActivationTokenRepository userActivationTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final FilesStorageService storageService;
    private final RoleRepository roleRepository;
    private final CompanyService companyService;
    private final EmailService emailService;
    private final MessageSource messageSource;

    public List<User> allUsers() {
        List<User> users = new ArrayList<>();
        userRepository.findAll().forEach(users::add);
        return users;
    }

    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    public String getMainRole(String email) {
        String returnRole;
        final String adminRole = "Admin";
        final String modRole = "Moderador";
        final String userRole = "Usuário";

        String role = userRepository.findByEmail(email).get().getRoles().stream().findFirst().get().getName().name();
        ERole roleName = ERole.valueOf(role);

        switch (roleName) {
            case ROLE_ADMIN:
                returnRole = adminRole;
                break;
            case ROLE_MODERATOR:
                returnRole = modRole;
                break;
            case ROLE_USER:
                returnRole = userRole;
                break;
            default:
                returnRole = userRole;
                break;
        }

        return returnRole;
    }

    public boolean validateUserBeforeSave(Authentication authentication) {
        if (authentication.getPrincipal().equals("anonymousUser")) {
            return false;
        } else {
            // Get the username from the principal
            String username = authentication.getName();
            Optional<User> optionalUser = userRepository.findByEmail(username);

            return optionalUser.isPresent();
        }
    }

    public User updateUser(User currentUser, MultipartFile file, UpdateUserDetailsDto updateUserDetailsDto) {
        /* Upload Photo */
        String fileName = "";
        if (file != null) {
            fileName = currentUser.getEmail() + Constants.EQUALS + file.getOriginalFilename();
            try {
                storageService.save(file, currentUser.getEmail());
            } catch (Exception e) {
                LOGGER.error("Could not upload the image: " + fileName + ". Error: " + e.getMessage());
            }
        }

        if (fileName.isEmpty()) {
            fileName = currentUser.getPhoto();
        }
        currentUser.setFullName(updateUserDetailsDto.fullName());
        // Company and email are not editable in user profile - preserve existing values
        currentUser.setEmail(currentUser.getEmail());
        currentUser.setPhone(updateUserDetailsDto.phone());
        currentUser.setCountry(updateUserDetailsDto.country());
        currentUser.setLanguage(updateUserDetailsDto.language());
        currentUser.setCity(updateUserDetailsDto.city());
        currentUser.setWork_format(updateUserDetailsDto.workFormat());
        currentUser.setPosition(updateUserDetailsDto.position());
        currentUser.setJoined_date(updateUserDetailsDto.joinedDate());

        if (file == null && fileName == null)
            currentUser.setPhoto(null);
        else
            currentUser.setPhoto(fileName);

        return userRepository.save(currentUser);
    }

    /**
     * Returns the current authenticated user from SecurityContextHolder
     * 
     * @return The current authenticated User object or null if not authenticated
     */
    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated() || 
            authentication.getPrincipal().equals("anonymousUser")) {
            return null;
        }

        if (authentication.getPrincipal() instanceof User) {
            return (User) authentication.getPrincipal();
        } else {
            // If principal is not a User object, try to find by username
            String username = authentication.getName();
            return userRepository.findByEmail(username).orElse(null);
        }
    }

    public Optional<User> findUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    public Optional<User> findById(Integer id) {
        return userRepository.findById(id);
    }

    @Transactional
    public String createPasswordResetTokenForUser(User user) {
        // Check for existing token and delete it to prevent duplicate key constraint violations
        Optional<PasswordResetToken> existingToken = passwordResetTokenRepository.findByUser(user);
        if (existingToken.isPresent()) {
            LOGGER.info("Deleting existing password reset token for user: {}", user.getEmail());
            passwordResetTokenRepository.delete(existingToken.get());
            // Flush to ensure the deletion is committed before creating a new token
            passwordResetTokenRepository.flush();
        }

        // Create new token
        String token = UUID.randomUUID().toString();
        PasswordResetToken myToken = new PasswordResetToken(token, user);
        PasswordResetToken savedToken = passwordResetTokenRepository.save(myToken);

        LOGGER.info("Created new password reset token for user: {} (Token ID: {})", user.getEmail(), savedToken.getId());
        return token;
    }

    public Optional<PasswordResetToken> getPasswordResetToken(String token) {
        return passwordResetTokenRepository.findByToken(token);
    }

    public void changeUserPassword(User user, String newPassword) {
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    @Transactional
    public void deletePasswordResetToken(String token) {
        passwordResetTokenRepository.findByToken(token).ifPresent(passwordResetTokenRepository::delete);
    }

    @Transactional
    public String createUserActivationTokenForUser(User user) {
        // Check for existing token and delete it to prevent duplicate key constraint violations
        Optional<UserActivationToken> existingToken = userActivationTokenRepository.findByUser(user);
        if (existingToken.isPresent()) {
            LOGGER.info("Deleting existing user activation token for user: {}", user.getEmail());
            userActivationTokenRepository.delete(existingToken.get());
            // Flush to ensure the deletion is committed before creating a new token
            userActivationTokenRepository.flush();
        }

        // Create new token
        String token = UUID.randomUUID().toString();
        UserActivationToken myToken = new UserActivationToken(token, user);
        UserActivationToken savedToken = userActivationTokenRepository.save(myToken);

        LOGGER.info("Created new user activation token for user: {} (Token ID: {})", user.getEmail(), savedToken.getId());
        return token;
    }

    public Optional<UserActivationToken> getUserActivationToken(String token) {
        return userActivationTokenRepository.findByToken(token);
    }

    @Transactional
    public void deleteUserActivationToken(String token) {
        userActivationTokenRepository.findByToken(token).ifPresent(userActivationTokenRepository::delete);
    }

    /**
     * Save a user to the database
     * 
     * @param user The user to save
     * @return The saved user
     */
    public User saveUser(User user) {
        return userRepository.save(user);
    }

    // ===== ADMIN USER MANAGEMENT METHODS =====

    /**
     * Get users with filtering for admin management
     */
    public Page<User> getUsersWithFilters(String fullName, String email, String company,
                                         ERole role, Boolean enabled, Boolean accountLocked, Pageable pageable) {
        return userRepository.findUsersWithFilters(fullName, email, company, role, enabled, accountLocked, pageable);
    }

    /**
     * Get users that don't belong to a specific company with filtering
     */
    public Page<User> getUsersNotInCompany(String companyName, String fullName, String email,
                                          ERole role, Boolean enabled, Boolean accountLocked, Pageable pageable) {
        return userRepository.findUsersNotInCompany(companyName, fullName, email, role, enabled, accountLocked, pageable);
    }

    /**
     * Convert User entity to UserListDto for admin listing
     */
    public UserListDto toUserListDto(User user) {
        Set<ERole> roles = user.getRoles().stream()
                .map(role -> role.getName())
                .collect(Collectors.toSet());

        return new UserListDto(
            user.getId(),
            user.getFullName(),
            user.getEmail(),
            user.getCompany(),
            user.getPhone(),
            user.getCountry(),
            user.getCity(),
            user.getPosition(),
            user.isEnabled(),
            user.isAccountLocked(),
            user.getCreatedAt(),
            user.getUpdatedAt(),
            roles,
            user.getPhoto()
        );
    }

    /**
     * Convert User entity to EditUserDto for editing
     */
    public EditUserDto toEditUserDto(User user) {
        // Get the first role (since we now only allow one role)
        ERole role = user.getRoles().stream()
                .map(r -> r.getName())
                .findFirst()
                .orElse(ERole.ROLE_USER); // Default to USER if no role found

        // Convert company name to company ID
        Long companyId = null;
        if (user.getCompany() != null && !user.getCompany().trim().isEmpty()) {
            // Find company by name to get ID
            List<Company> companies = companyService.getAllCompaniesForDropdown();
            companyId = companies.stream()
                    .filter(company -> company.getCompanyName().equals(user.getCompany()))
                    .map(Company::getId)
                    .findFirst()
                    .orElse(null);
        }

        return new EditUserDto(
            user.getId(),
            user.getFullName(),
            user.getEmail(),
            companyId,
            user.getPhone(),
            user.getCountry(),
            user.getLanguage(),
            user.getCity(),
            user.getWork_format(),
            user.getPosition(),
            user.getJoined_date(),
            user.isEnabled(),
            user.isAccountLocked(),
            role
        );
    }

    /**
     * Create a new user from CreateUserDto (admin function)
     */
    @Transactional
    public User createUser(CreateUserDto createUserDto) {
        LOGGER.info("UserService.createUser called with: {}", createUserDto);

        // Check if user already exists
        if (userRepository.existsByEmail(createUserDto.email())) {
            throw new RuntimeException("User with email " + createUserDto.email() + " already exists");
        }

        User user = new User();
        // Explicitly ensure ID is null for new entities
        user.setId(null);
        user.setFullName(createUserDto.fullName());
        user.setEmail(createUserDto.email());
        user.setPassword(passwordEncoder.encode(createUserDto.password()));

        // Convert company ID to company name
        String companyName = null;
        if (createUserDto.companyId() != null) {
            Optional<Company> company = companyService.getCompanyById(createUserDto.companyId());
            companyName = company.map(Company::getCompanyName).orElse(null);
        }
        user.setCompany(companyName);

        user.setPhone(createUserDto.phone());
        user.setCountry(createUserDto.country());
        user.setLanguage(createUserDto.language());
        user.setCity(createUserDto.city());
        user.setWork_format(createUserDto.workFormat());
        user.setPosition(createUserDto.position());
        user.setJoined_date(createUserDto.joinedDate());
        user.setEnabled(createUserDto.enabled());

        // Set role (single role)
        Set<Role> roles = new HashSet<>();
        if (createUserDto.roles() != null) {
            Role role = roleRepository.findByName(createUserDto.roles())
                    .orElseThrow(() -> new RuntimeException("Role not found: " + createUserDto.roles()));
            roles.add(role);
        } else {
            // Default to USER role if no role specified
            Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                    .orElseThrow(() -> new RuntimeException("Default USER role not found"));
            roles.add(userRole);
        }
        user.setRoles(roles);

        LOGGER.info("About to save user: ID={}, Email={}", user.getId(), user.getEmail());

        try {
            User savedUser = userRepository.save(user);
            LOGGER.info("User saved successfully with ID: {}", savedUser.getId());

            // Check if user was created as inactive
            if (!savedUser.isEnabled()) {
                LOGGER.info("User created as inactive, sending activation email: {}", savedUser.getEmail());
                try {
                    // Create activation token
                    String token = createUserActivationTokenForUser(savedUser);

                    // Construct activation URL
                    String activationUrl = baseUrl + "/activate-user?lang=" + savedUser.getLanguage() + "&token=" + token;

                    // Create locale from user's language or default to system locale
                    Locale userLocale;

                    // If the language is Portuguese but no country is specified, use Brazilian Portuguese
                    if (savedUser.getLanguage().equals("pt")) {
                        userLocale = new Locale("pt", "BR");
                        LOGGER.debug("Adjusted locale to Brazilian Portuguese: {}", userLocale);
                        System.out.println("[DEBUG_LOG] Adjusted locale to Brazilian Portuguese: " + userLocale);
                    }else {
                        userLocale = Locale.getDefault();
                        LOGGER.info("Using default locale for email: {}", userLocale);
                    }

                    // Get translated email subject
                    String emailSubject = messageSource.getMessage("email.user.activation.title", null, "Complete Your User Registration", userLocale);
                    LOGGER.info("Using translated email subject: {}", emailSubject);

                    emailService.sendUserActivationEmail(savedUser.getEmail(), emailSubject, activationUrl, userLocale);

                    LOGGER.info("Activation email sent successfully to: {}", savedUser.getEmail());
                } catch (Exception e) {
                    LOGGER.error("Error sending activation email to {}: {}", savedUser.getEmail(), e.getMessage(), e);
                    // We don't throw the exception here to avoid rolling back the user creation
                    // The user is still created, but the activation email failed
                }
            }

            return savedUser;
        } catch (Exception e) {
            LOGGER.error("Error saving user: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Update an existing user from EditUserDto (admin function)
     */
    @Transactional
    public User updateUserAdmin(EditUserDto editUserDto) {
        User user = userRepository.findById(editUserDto.id())
                .orElseThrow(() -> new RuntimeException("User not found with id: " + editUserDto.id()));

        // Check if email is being changed and if new email already exists
        if (!user.getEmail().equals(editUserDto.email()) &&
            userRepository.existsByEmail(editUserDto.email())) {
            throw new RuntimeException("User with email " + editUserDto.email() + " already exists");
        }

        user.setFullName(editUserDto.fullName());
        user.setEmail(editUserDto.email());

        // Convert company ID to company name
        String companyName = null;
        if (editUserDto.companyId() != null) {
            Optional<Company> company = companyService.getCompanyById(editUserDto.companyId());
            companyName = company.map(Company::getCompanyName).orElse(null);
        }
        user.setCompany(companyName);

        user.setPhone(editUserDto.phone());
        user.setCountry(editUserDto.country());
        user.setLanguage(editUserDto.language());
        user.setCity(editUserDto.city());
        user.setWork_format(editUserDto.workFormat());
        user.setPosition(editUserDto.position());
        user.setJoined_date(editUserDto.joinedDate());
        user.setEnabled(editUserDto.enabled());
        user.setAccountLocked(editUserDto.accountLocked());

        // Update role (single role)
        Set<Role> roles = new HashSet<>();
        if (editUserDto.roles() != null) {
            Role role = roleRepository.findByName(editUserDto.roles())
                    .orElseThrow(() -> new RuntimeException("Role not found: " + editUserDto.roles()));
            roles.add(role);
        } else {
            // Check if user currently has ADMIN role
            boolean isCurrentlyAdmin = user.getRoles().stream()
                    .anyMatch(role -> role.getName() == ERole.ROLE_ADMIN);

            if (isCurrentlyAdmin) {
                // Keep ADMIN role if user is currently ADMIN and no role was selected
                Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                        .orElseThrow(() -> new RuntimeException("ADMIN role not found"));
                roles.add(adminRole);
            } else {
                // Default to USER role if no role specified and user is not currently ADMIN
                Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                        .orElseThrow(() -> new RuntimeException("Default USER role not found"));
                roles.add(userRole);
            }
        }
        user.setRoles(roles);

        return userRepository.save(user);
    }

    /**
     * Delete a user by ID (admin function)
     */
    @Transactional
    public void deleteUser(Integer userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        // Don't allow deletion of the current user
        User currentUser = getCurrentUser();
        if (currentUser != null && currentUser.getId().equals(userId)) {
            throw new RuntimeException("Cannot delete your own account");
        }

        userRepository.delete(user);
    }

    /**
     * Delete a user by email (admin function)
     */
    @Transactional
    public void deleteUserByEmail(String email) {
        LOGGER.info("UserService.deleteUserByEmail called with email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));

        // Don't allow deletion of the current user
        User currentUser = getCurrentUser();
        if (currentUser != null && currentUser.getEmail().equals(email)) {
            throw new RuntimeException("Cannot delete your own account");
        }

        LOGGER.info("Deleting user: ID={}, Email={}", user.getId(), user.getEmail());
        userRepository.delete(user);
        LOGGER.info("User deleted successfully");
    }

    /**
     * Get distinct countries for filter dropdown
     */
    public List<String> getDistinctCountries() {
        return userRepository.findDistinctCountries();
    }

    /**
     * Get distinct companies for filter dropdown
     */
    public List<String> getDistinctCompanies() {
        return userRepository.findDistinctCompanies();
    }

    /**
     * Get all available roles
     */
    public List<Role> getAllRoles() {
        return roleRepository.findAll();
    }

    /**
     * Get distinct roles for filter dropdown
     */
    public List<ERole> getDistinctRoles() {
        return userRepository.findDistinctRoles();
    }

    // ===== USER STATISTICS METHODS =====

    /**
     * Get total count of all users
     */
    public long getTotalUsersCount() {
        return userRepository.count();
    }

    /**
     * Get count of active/enabled users
     */
    public long getActiveUsersCount() {
        return userRepository.countEnabledUsers();
    }

    /**
     * Get count of inactive/disabled users
     */
    public long getInactiveUsersCount() {
        return userRepository.countDisabledUsers();
    }

    /**
     * Get count of locked users
     */
    public long getLockedUsersCount() {
        return userRepository.countLockedUsers();
    }

    /**
     * Deactivate a user by email (admin function)
     */
    @Transactional
    public void deactivateUserByEmail(String email) {
        LOGGER.info("UserService.deactivateUserByEmail called with email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));

        // Don't allow deactivation of the current user
        User currentUser = getCurrentUser();
        if (currentUser != null && currentUser.getEmail().equals(email)) {
            throw new RuntimeException("Cannot deactivate your own account");
        }

        user.setEnabled(false);
        userRepository.save(user);
        LOGGER.info("User deactivated successfully: {}", email);
    }

    /**
     * Activate a user by email (admin function)
     */
    @Transactional
    public void activateUserByEmail(String email) {
        LOGGER.info("UserService.activateUserByEmail called with email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));

        user.setEnabled(true);
        userRepository.save(user);
        LOGGER.info("User activated successfully: {}", email);
    }

    /**
     * Lock a user account by email (admin function)
     */
    @Transactional
    public void lockUserByEmail(String email) {
        LOGGER.info("UserService.lockUserByEmail called with email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));

        // Don't allow locking of the current user
        User currentUser = getCurrentUser();
        if (currentUser != null && currentUser.getEmail().equals(email)) {
            throw new RuntimeException("Cannot lock your own account");
        }

        user.setAccountLocked(true);
        user.setLockTime(new Date());
        userRepository.save(user);
        LOGGER.info("User locked successfully: {}", email);
    }

    /**
     * Unlock a user account by email (admin function)
     */
    @Transactional
    public void unlockUserByEmail(String email) {
        LOGGER.info("UserService.unlockUserByEmail called with email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));

        user.setAccountLocked(false);
        user.setLockTime(null);
        user.setFailedAttempt(0);
        userRepository.save(user);
        LOGGER.info("User unlocked successfully: {}", email);
    }
}
